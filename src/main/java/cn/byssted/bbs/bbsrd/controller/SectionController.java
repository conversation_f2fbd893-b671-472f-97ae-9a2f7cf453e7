package cn.byssted.bbs.bbsrd.controller;

import cn.byssted.bbs.bbsrd.annotation.AdminRequired;
import cn.byssted.bbs.bbsrd.common.Result;
import cn.byssted.bbs.bbsrd.entity.Section;
import cn.byssted.bbs.bbsrd.service.SectionService;
import cn.byssted.bbs.bbsrd.util.JwtUtil;
import io.swagger.v3.oas.annotations.OpenAPI31;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 板块控制器
 */
@RestController
@RequestMapping("/api/sections")
public class SectionController {

    @Autowired
    private SectionService sectionService;

    /**
     * 获取所有板块
     */
    @GetMapping
    public Result<List<Section>> getAllSections() {
        try {
            List<Section> sections = sectionService.getAllSections();
            return Result.success(sections);
        } catch (Exception e) {
            return Result.error("获取板块列表失败：" + e.getMessage());
        }
    }

    /**
     * 根据ID获取板块
     */
    @GetMapping("/{id}")
    public Result<Section> getSectionById(@PathVariable Integer id) {
        try {
            Section section = sectionService.getSectionById(id);
            if (section == null) {
                return Result.notFound("板块不存在");
            }
            return Result.success(section);
        } catch (Exception e) {
            return Result.error("获取板块信息失败：" + e.getMessage());
        }
    }

    /**
     * 创建板块（管理员功能）
     */
    @AdminRequired
    @io.swagger.v3.oas.annotations.parameters.RequestBody()
    @PostMapping
    public Result<Section> createSection(@RequestBody Map<String, String> request) {
        try {
            String name = request.get("name");
            String description = request.get("description");

            if (name == null || name.trim().isEmpty()) {
                return Result.badRequest("板块名称不能为空");
            }

            Section section = sectionService.createSection(name, description);
            return Result.success("创建成功", section);
        } catch (Exception e) {
            return Result.error("创建板块失败：" + e.getMessage());
        }
    }

    /**
     * 更新板块（管理员功能）
     */
    @AdminRequired
    @PutMapping("/{id}")
    public Result<String> updateSection(@PathVariable Integer id, @RequestBody Map<String, String> request) {
        try {
            Section section = sectionService.getSectionById(id);
            if (section == null) {
                return Result.notFound("板块不存在");
            }

            String name = request.get("name");
            String description = request.get("description");

            if (name != null) {
                section.setName(name);
            }
            if (description != null) {
                section.setDescription(description);
            }

            boolean success = sectionService.updateSection(section);
            if (success) {
                return Result.success("更新成功");
            } else {
                return Result.error("更新失败");
            }
        } catch (Exception e) {
            return Result.error("更新板块失败：" + e.getMessage());
        }
    }

    /**
     * 删除板块（管理员功能）
     */
    @AdminRequired
    @DeleteMapping("/{id}")
    public Result<String> deleteSection(@PathVariable Integer id) {
        try {
            boolean success = sectionService.deleteSection(id);
            if (success) {
                return Result.success("删除成功");
            } else {
                return Result.error("删除失败");
            }
        } catch (Exception e) {
            return Result.error("删除板块失败：" + e.getMessage());
        }
    }
}
